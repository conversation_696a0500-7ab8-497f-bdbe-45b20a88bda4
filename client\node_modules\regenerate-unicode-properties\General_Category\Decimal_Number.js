const set = require('regenerate')();
set.addRange(0x30, 0x39).addRange(0x660, 0x669).addRange(0x6F0, 0x6F9).addRange(0x7C0, 0x7C9).addRange(0x966, 0x96F).addRange(0x9E6, 0x9EF).addRange(0xA66, 0xA6F).addRange(0xAE6, 0xAEF).addRange(0xB66, 0xB6F).addRange(0xBE6, 0xBEF).addRange(0xC66, 0xC6F).addRange(0xCE6, 0xCEF).addRange(0xD66, 0xD6F).addRange(0xDE6, 0xDEF).addRange(0xE50, 0xE59).addRange(0xED0, 0xED9).addRange(0xF20, 0xF29).addRange(0x1040, 0x1049).addRange(0x1090, 0x1099).addRange(0x17E0, 0x17E9).addRange(0x1810, 0x1819).addRange(0x1946, 0x194F).addRange(0x19D0, 0x19D9).addRange(0x1A80, 0x1A89).addRange(0x1A90, 0x1A99).addRange(0x1B50, 0x1B59).addRange(0x1BB0, 0x1BB9).addRange(0x1C40, 0x1C49).addRange(0x1C50, 0x1C59).addRange(0xA620, 0xA629).addRange(0xA8D0, 0xA8D9).addRange(0xA900, 0xA909).addRange(0xA9D0, 0xA9D9).addRange(0xA9F0, 0xA9F9).addRange(0xAA50, 0xAA59).addRange(0xABF0, 0xABF9).addRange(0xFF10, 0xFF19).addRange(0x104A0, 0x104A9).addRange(0x10D30, 0x10D39).addRange(0x10D40, 0x10D49).addRange(0x11066, 0x1106F).addRange(0x110F0, 0x110F9).addRange(0x11136, 0x1113F).addRange(0x111D0, 0x111D9).addRange(0x112F0, 0x112F9).addRange(0x11450, 0x11459).addRange(0x114D0, 0x114D9).addRange(0x11650, 0x11659).addRange(0x116C0, 0x116C9).addRange(0x116D0, 0x116E3).addRange(0x11730, 0x11739);
set.addRange(0x118E0, 0x118E9).addRange(0x11950, 0x11959).addRange(0x11BF0, 0x11BF9).addRange(0x11C50, 0x11C59).addRange(0x11D50, 0x11D59).addRange(0x11DA0, 0x11DA9).addRange(0x11F50, 0x11F59).addRange(0x16130, 0x16139).addRange(0x16A60, 0x16A69).addRange(0x16AC0, 0x16AC9).addRange(0x16B50, 0x16B59).addRange(0x16D70, 0x16D79).addRange(0x1CCF0, 0x1CCF9).addRange(0x1D7CE, 0x1D7FF).addRange(0x1E140, 0x1E149).addRange(0x1E2F0, 0x1E2F9).addRange(0x1E4F0, 0x1E4F9).addRange(0x1E5F1, 0x1E5FA).addRange(0x1E950, 0x1E959).addRange(0x1FBF0, 0x1FBF9);
exports.characters = set;
