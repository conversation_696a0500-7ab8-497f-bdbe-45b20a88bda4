/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 * @format
 */

export type ____DangerouslyImpreciseStyle_InternalOverrides = $ReadOnly<{}>;
export type ____ImageStyle_InternalOverrides = $ReadOnly<{}>;
export type ____ShadowStyle_InternalOverrides = $ReadOnly<{}>;
export type ____TextStyle_InternalOverrides = $ReadOnly<{}>;
export type ____ViewStyle_InternalOverrides = $ReadOnly<{}>;
