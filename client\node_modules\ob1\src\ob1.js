"use strict";

function add(a, b) {
  return a + b;
}
function sub(a, b) {
  return a - b;
}
function get0(x) {
  return x;
}
function get1(x) {
  return x;
}
function add1(x) {
  return x + 1;
}
function sub1(x) {
  return x - 1;
}
function neg(x) {
  return -x;
}
function add0(x) {
  return x;
}
function inc(x) {
  return x + 1;
}
module.exports = {
  add,
  get0,
  get1,
  add1,
  sub1,
  sub,
  neg,
  add0,
  inc,
};
